using UnityEngine;

/// <summary>
/// 噪声地图生成器 - 负责地形生成和显示
/// 基于Red Blob Games技术方案实现随机地图生成
/// </summary>
public class NoiseMap : MonoBehaviour
{
    [Header("配置")]
    [Tooltip("噪声配置文件")]
    [SerializeField] private NoiseConfigSO noiseConfig;
    
    [Header("显示设置")]
    [Tooltip("地图渲染器")]
    [SerializeField] private Renderer mapRenderer;
    
    [Tooltip("自动生成地图")]
    [SerializeField] private bool autoGenerate = true;
    
    [Tooltip("显示调试信息")]
    [SerializeField] private bool showDebugInfo = true;

    // 私有变量
    private Texture2D mapTexture;
    private float[,] heightMap;
    private Color[] colorMap;
    
    // 调试信息
    private string debugInfo = "";
    private float lastGenerationTime = 0f;

    void Start()
    {
        InitializeMap();
        
        if (autoGenerate)
        {
            GenerateMap();
        }
    }

    void OnEnable()
    {
        // 订阅配置变化事件
        if (noiseConfig != null)
        {
            noiseConfig.OnConfigChanged += OnConfigChanged;
        }
    }

    void OnDisable()
    {
        // 取消订阅配置变化事件
        if (noiseConfig != null)
        {
            noiseConfig.OnConfigChanged -= OnConfigChanged;
        }
    }

    /// <summary>
    /// 初始化地图
    /// </summary>
    private void InitializeMap()
    {
        if (noiseConfig == null)
        {
            Debug.LogError("NoiseMap: 缺少噪声配置文件！");
            return;
        }

        // 创建纹理
        mapTexture = new Texture2D(noiseConfig.MapWidth, noiseConfig.MapHeight);
        mapTexture.filterMode = FilterMode.Point;
        mapTexture.wrapMode = TextureWrapMode.Clamp;

        // 初始化高度图和颜色图
        heightMap = new float[noiseConfig.MapWidth, noiseConfig.MapHeight];
        colorMap = new Color[noiseConfig.MapWidth * noiseConfig.MapHeight];

        // 设置渲染器材质
        if (mapRenderer != null)
        {
            mapRenderer.material.mainTexture = mapTexture;
        }
    }

    /// <summary>
    /// 生成地图
    /// </summary>
    public void GenerateMap()
    {
        if (noiseConfig == null)
        {
            Debug.LogError("NoiseMap: 缺少噪声配置文件！");
            return;
        }

        float startTime = Time.realtimeSinceStartup;

        // 生成高度图
        GenerateHeightMap();
        
        // 生成颜色图
        GenerateColorMap();
        
        // 更新纹理
        UpdateTexture();

        lastGenerationTime = Time.realtimeSinceStartup - startTime;
        
        if (showDebugInfo)
        {
            UpdateDebugInfo();
        }

        Debug.Log($"地图生成完成，耗时: {lastGenerationTime:F3}秒");
    }

    /// <summary>
    /// 生成高度图
    /// </summary>
    private void GenerateHeightMap()
    {
        int width = noiseConfig.MapWidth;
        int height = noiseConfig.MapHeight;

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                // 计算归一化坐标 (0-1)
                float normalizedX = (float)x / width + noiseConfig.OffsetX;
                float normalizedY = (float)y / height + noiseConfig.OffsetY;

                float heightValue;

                if (noiseConfig.IslandMode)
                {
                    // 岛屿模式 - 使用边缘衰减
                    heightValue = NoiseAlgorithm.IslandNoise2D(
                        normalizedX, normalizedY,
                        noiseConfig.Octaves,
                        noiseConfig.Frequency,
                        noiseConfig.Amplitude,
                        noiseConfig.Lacunarity,
                        noiseConfig.Persistence,
                        noiseConfig.FalloffStrength,
                        noiseConfig.Seed
                    );
                }
                else
                {
                    // 标准分形噪声
                    heightValue = NoiseAlgorithm.FractalNoise2D(
                        normalizedX, normalizedY,
                        noiseConfig.Octaves,
                        noiseConfig.Frequency,
                        noiseConfig.Amplitude,
                        noiseConfig.Lacunarity,
                        noiseConfig.Persistence,
                        noiseConfig.Seed
                    );
                }

                // 混合脊状噪声
                if (noiseConfig.UseRidgedNoise)
                {
                    float ridgedNoise = NoiseAlgorithm.RidgedNoise2D(
                        normalizedX * noiseConfig.Frequency,
                        normalizedY * noiseConfig.Frequency,
                        noiseConfig.Seed
                    );
                    
                    heightValue = Mathf.Lerp(heightValue, ridgedNoise, noiseConfig.RidgedNoiseBlend);
                }

                // 应用高度重分布
                heightValue = NoiseAlgorithm.RedistributeHeight(heightValue, noiseConfig.RedistributionExponent);

                // 应用边缘平滑
                if (noiseConfig.EnableEdgeSmoothing && !noiseConfig.IslandMode)
                {
                    heightValue = ApplyEdgeSmoothing(heightValue, x, y, width, height);
                }

                heightMap[x, y] = Mathf.Clamp01(heightValue);
            }
        }
    }

    /// <summary>
    /// 应用边缘平滑
    /// </summary>
    private float ApplyEdgeSmoothing(float height, int x, int y, int width, int height_param)
    {
        float smoothRange = noiseConfig.EdgeSmoothingRange;
        float smoothStrength = noiseConfig.EdgeSmoothingStrength;

        // 计算到边缘的最小距离
        float distanceToEdgeX = Mathf.Min(x, width - 1 - x) / (float)width;
        float distanceToEdgeY = Mathf.Min(y, height_param - 1 - y) / (float)height_param;
        float minDistanceToEdge = Mathf.Min(distanceToEdgeX, distanceToEdgeY);

        // 如果在平滑范围内，应用平滑
        if (minDistanceToEdge < smoothRange)
        {
            float smoothFactor = NoiseAlgorithm.SmoothStep(minDistanceToEdge, 0f, smoothRange);
            smoothFactor = Mathf.Pow(smoothFactor, smoothStrength);
            height *= smoothFactor;
        }

        return height;
    }

    /// <summary>
    /// 生成颜色图
    /// </summary>
    private void GenerateColorMap()
    {
        int width = noiseConfig.MapWidth;
        int height = noiseConfig.MapHeight;

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                float heightValue = heightMap[x, y];
                Color pixelColor = GetColorFromHeight(heightValue);
                colorMap[y * width + x] = pixelColor;
            }
        }
    }

    /// <summary>
    /// 根据高度值获取颜色
    /// </summary>
    private Color GetColorFromHeight(float height)
    {
        // 简单的高度到颜色映射
        if (height < 0.2f)
            return Color.Lerp(new Color(0.1f, 0.2f, 0.8f), new Color(0.2f, 0.4f, 0.9f), height / 0.2f); // 深蓝到浅蓝 (水)
        else if (height < 0.25f)
            return Color.Lerp(new Color(0.8f, 0.8f, 0.6f), new Color(0.9f, 0.9f, 0.7f), (height - 0.2f) / 0.05f); // 沙滩
        else if (height < 0.5f)
            return Color.Lerp(new Color(0.2f, 0.6f, 0.2f), new Color(0.3f, 0.7f, 0.3f), (height - 0.25f) / 0.25f); // 草地
        else if (height < 0.7f)
            return Color.Lerp(new Color(0.1f, 0.4f, 0.1f), new Color(0.2f, 0.5f, 0.2f), (height - 0.5f) / 0.2f); // 森林
        else if (height < 0.85f)
            return Color.Lerp(new Color(0.5f, 0.4f, 0.3f), new Color(0.6f, 0.5f, 0.4f), (height - 0.7f) / 0.15f); // 山地
        else
            return Color.Lerp(new Color(0.8f, 0.8f, 0.8f), Color.white, (height - 0.85f) / 0.15f); // 雪山
    }

    /// <summary>
    /// 更新纹理
    /// </summary>
    private void UpdateTexture()
    {
        if (mapTexture != null)
        {
            mapTexture.SetPixels(colorMap);
            mapTexture.Apply();
        }
    }

    /// <summary>
    /// 配置变化回调
    /// </summary>
    private void OnConfigChanged()
    {
        if (noiseConfig.EnableRealTimeUpdate)
        {
            // 重新初始化（如果尺寸改变）
            if (mapTexture == null || 
                mapTexture.width != noiseConfig.MapWidth || 
                mapTexture.height != noiseConfig.MapHeight)
            {
                InitializeMap();
            }
            
            GenerateMap();
        }
    }

    /// <summary>
    /// 更新调试信息
    /// </summary>
    private void UpdateDebugInfo()
    {
        debugInfo = $"地图生成调试信息:\n" +
                   $"{noiseConfig.GetConfigSummary()}\n" +
                   $"生成时间: {lastGenerationTime:F3}秒\n" +
                   $"像素总数: {noiseConfig.MapWidth * noiseConfig.MapHeight:N0}";
    }

    /// <summary>
    /// 手动触发地图生成
    /// </summary>
    [ContextMenu("生成地图")]
    public void ManualGenerateMap()
    {
        GenerateMap();
    }

    /// <summary>
    /// 随机化种子并生成新地图
    /// </summary>
    [ContextMenu("随机生成")]
    public void RandomizeAndGenerate()
    {
        if (noiseConfig != null)
        {
            noiseConfig.RandomizeSeed();
        }
    }

    // GUI显示调试信息
    void OnGUI()
    {
        if (showDebugInfo && !string.IsNullOrEmpty(debugInfo))
        {
            GUI.Box(new Rect(10, 10, 300, 100), debugInfo);
        }
    }
}
