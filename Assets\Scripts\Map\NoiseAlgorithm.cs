using UnityEngine;

/// <summary>
/// 噪声算法类 - 提供各种噪声生成算法
/// 基于Red Blob Games的技术方案实现
/// </summary>
public static class NoiseAlgorithm
{
    // 柏林噪声置换表
    private static readonly int[] permutation = {
        151,160,137,91,90,15,131,13,201,95,96,53,194,233,7,225,140,36,103,30,69,142,8,99,37,240,21,10,23,
        190,6,148,247,120,234,75,0,26,197,62,94,252,219,203,117,35,11,32,57,177,33,88,237,149,56,87,174,20,125,136,171,168,68,175,74,165,71,134,139,48,27,166,
        77,146,158,231,83,111,229,122,60,211,133,230,220,105,92,41,55,46,245,40,244,102,143,54,65,25,63,161,1,216,80,73,209,76,132,187,208,89,18,169,
        200,196,135,130,116,188,159,86,164,100,109,198,173,186,3,64,52,217,226,250,124,123,5,202,38,147,118,126,255,82,85,212,207,206,59,227,47,16,
        58,17,182,189,28,42,223,183,170,213,119,248,152,2,44,154,163,70,221,153,101,155,167,43,172,9,129,22,39,253,19,98,108,110,79,113,224,232,178,185,
        112,104,218,246,97,228,251,34,242,193,238,210,144,12,191,179,162,241,81,51,145,235,249,14,239,107,49,192,214,31,181,199,106,157,184,84,204,176,115,121,50,45,127,4,150,254,
        138,236,205,93,222,114,67,29,24,72,243,141,128,195,78,66,215,61,156,180
    };

    // 扩展置换表（重复一次以避免边界检查）
    private static readonly int[] p;

    static NoiseAlgorithm()
    {
        p = new int[512];
        for (int i = 0; i < 512; i++)
        {
            p[i] = permutation[i % 256];
        }
    }

    /// <summary>
    /// 生成2D柏林噪声
    /// </summary>
    /// <param name="x">X坐标</param>
    /// <param name="y">Y坐标</param>
    /// <param name="seed">随机种子</param>
    /// <returns>噪声值 (0.0 - 1.0)</returns>
    public static float PerlinNoise2D(float x, float y, int seed = 0)
    {
        // 应用种子偏移
        x += seed * 0.1f;
        y += seed * 0.1f;

        // 计算网格坐标
        int X = Mathf.FloorToInt(x) & 255;
        int Y = Mathf.FloorToInt(y) & 255;

        // 计算相对坐标
        x -= Mathf.Floor(x);
        y -= Mathf.Floor(y);

        // 计算淡化曲线
        float u = Fade(x);
        float v = Fade(y);

        // 哈希坐标
        int A = p[X] + Y;
        int AA = p[A];
        int AB = p[A + 1];
        int B = p[X + 1] + Y;
        int BA = p[B];
        int BB = p[B + 1];

        // 插值计算
        float result = Lerp(v,
            Lerp(u, Grad(p[AA], x, y), Grad(p[BA], x - 1, y)),
            Lerp(u, Grad(p[AB], x, y - 1), Grad(p[BB], x - 1, y - 1))
        );

        // 将结果从 [-1, 1] 转换到 [0, 1]
        return (result + 1.0f) * 0.5f;
    }

    /// <summary>
    /// 生成多层噪声（分形噪声）
    /// </summary>
    /// <param name="x">X坐标</param>
    /// <param name="y">Y坐标</param>
    /// <param name="octaves">倍频程数量</param>
    /// <param name="frequency">基础频率</param>
    /// <param name="amplitude">基础振幅</param>
    /// <param name="lacunarity">频率倍数</param>
    /// <param name="persistence">振幅衰减</param>
    /// <param name="seed">随机种子</param>
    /// <returns>噪声值 (0.0 - 1.0)</returns>
    public static float FractalNoise2D(float x, float y, int octaves, float frequency, 
        float amplitude, float lacunarity, float persistence, int seed = 0)
    {
        float value = 0.0f;
        float currentAmplitude = amplitude;
        float currentFrequency = frequency;
        float maxValue = 0.0f; // 用于归一化

        for (int i = 0; i < octaves; i++)
        {
            value += PerlinNoise2D(x * currentFrequency, y * currentFrequency, seed + i) * currentAmplitude;
            maxValue += currentAmplitude;
            
            currentAmplitude *= persistence;
            currentFrequency *= lacunarity;
        }

        // 归一化到 [0, 1]
        return value / maxValue;
    }

    /// <summary>
    /// 生成带边缘衰减的噪声（用于岛屿生成）
    /// </summary>
    /// <param name="x">X坐标 (0-1)</param>
    /// <param name="y">Y坐标 (0-1)</param>
    /// <param name="octaves">倍频程数量</param>
    /// <param name="frequency">基础频率</param>
    /// <param name="amplitude">基础振幅</param>
    /// <param name="lacunarity">频率倍数</param>
    /// <param name="persistence">振幅衰减</param>
    /// <param name="falloffStrength">边缘衰减强度</param>
    /// <param name="seed">随机种子</param>
    /// <returns>噪声值 (0.0 - 1.0)</returns>
    public static float IslandNoise2D(float x, float y, int octaves, float frequency,
        float amplitude, float lacunarity, float persistence, float falloffStrength, int seed = 0)
    {
        // 生成基础分形噪声
        float noise = FractalNoise2D(x, y, octaves, frequency, amplitude, lacunarity, persistence, seed);
        
        // 计算到中心的距离
        float dx = x - 0.5f;
        float dy = y - 0.5f;
        float distanceFromCenter = Mathf.Sqrt(dx * dx + dy * dy) * 2.0f; // 归一化到 [0, 1]
        
        // 应用边缘衰减
        float falloff = Mathf.Pow(Mathf.Max(0, 1.0f - distanceFromCenter), falloffStrength);
        
        return noise * falloff;
    }

    /// <summary>
    /// 生成脊状噪声
    /// </summary>
    /// <param name="x">X坐标</param>
    /// <param name="y">Y坐标</param>
    /// <param name="seed">随机种子</param>
    /// <returns>脊状噪声值 (0.0 - 1.0)</returns>
    public static float RidgedNoise2D(float x, float y, int seed = 0)
    {
        float noise = PerlinNoise2D(x, y, seed);
        return 1.0f - Mathf.Abs(2.0f * noise - 1.0f);
    }

    /// <summary>
    /// 淡化函数 - 6t^5 - 15t^4 + 10t^3
    /// </summary>
    private static float Fade(float t)
    {
        return t * t * t * (t * (t * 6 - 15) + 10);
    }

    /// <summary>
    /// 线性插值
    /// </summary>
    private static float Lerp(float t, float a, float b)
    {
        return a + t * (b - a);
    }

    /// <summary>
    /// 梯度函数
    /// </summary>
    private static float Grad(int hash, float x, float y)
    {
        int h = hash & 15;
        float u = h < 8 ? x : y;
        float v = h < 4 ? y : h == 12 || h == 14 ? x : 0;
        return ((h & 1) == 0 ? u : -u) + ((h & 2) == 0 ? v : -v);
    }

    /// <summary>
    /// 应用高度重分布曲线
    /// </summary>
    /// <param name="height">原始高度值</param>
    /// <param name="exponent">指数</param>
    /// <returns>重分布后的高度值</returns>
    public static float RedistributeHeight(float height, float exponent)
    {
        return Mathf.Pow(height, exponent);
    }

    /// <summary>
    /// 平滑阶梯函数 - 用于创建平滑的地形过渡
    /// </summary>
    /// <param name="value">输入值</param>
    /// <param name="edge0">下边界</param>
    /// <param name="edge1">上边界</param>
    /// <returns>平滑插值结果</returns>
    public static float SmoothStep(float value, float edge0, float edge1)
    {
        float t = Mathf.Clamp01((value - edge0) / (edge1 - edge0));
        return t * t * (3.0f - 2.0f * t);
    }
}
