using UnityEngine;

/// <summary>
/// 噪声配置ScriptableObject - 存储所有噪声生成参数
/// 支持实时调整参数并更新地图显示
/// </summary>
[CreateAssetMenu(fileName = "NoiseConfig", menuName = "Map/Noise Config", order = 1)]
public class NoiseConfigSO : ScriptableObject
{
    [Header("基础设置")]
    [Tooltip("随机种子，相同种子生成相同地形")]
    [SerializeField] private int seed = 12345;
    
    [Tooltip("地图宽度（像素）")]
    [SerializeField] private int mapWidth = 512;
    
    [Tooltip("地图高度（像素）")]
    [SerializeField] private int mapHeight = 512;

    [Header("噪声参数")]
    [Tooltip("倍频程数量 - 控制地形细节层次")]
    [Range(1, 8)]
    [SerializeField] private int octaves = 4;
    
    [Tooltip("基础频率 - 控制地形特征大小")]
    [Range(0.001f, 0.1f)]
    [SerializeField] private float frequency = 0.01f;
    
    [Tooltip("基础振幅 - 控制地形高度变化")]
    [Range(0.1f, 2.0f)]
    [SerializeField] private float amplitude = 1.0f;
    
    [Tooltip("频率倍数 - 每层噪声的频率倍增")]
    [Range(1.5f, 3.0f)]
    [SerializeField] private float lacunarity = 2.0f;
    
    [Tooltip("振幅衰减 - 每层噪声的振幅衰减")]
    [Range(0.1f, 0.8f)]
    [SerializeField] private float persistence = 0.5f;

    [Header("地形重分布")]
    [Tooltip("高度重分布指数 - 控制山谷和山峰的分布")]
    [Range(0.5f, 3.0f)]
    [SerializeField] private float redistributionExponent = 1.2f;
    
    [Tooltip("启用岛屿模式")]
    [SerializeField] private bool islandMode = false;
    
    [Tooltip("边缘衰减强度 - 岛屿模式下的边缘平滑度")]
    [Range(0.5f, 5.0f)]
    [SerializeField] private float falloffStrength = 2.0f;

    [Header("边缘平滑")]
    [Tooltip("启用边缘平滑过渡")]
    [SerializeField] private bool enableEdgeSmoothing = true;
    
    [Tooltip("边缘平滑范围 - 从边缘开始的平滑距离比例")]
    [Range(0.05f, 0.5f)]
    [SerializeField] private float edgeSmoothingRange = 0.1f;
    
    [Tooltip("边缘平滑强度 - 平滑过渡的强度")]
    [Range(0.1f, 2.0f)]
    [SerializeField] private float edgeSmoothingStrength = 1.0f;

    [Header("偏移设置")]
    [Tooltip("X轴偏移 - 用于地图平移")]
    [SerializeField] private float offsetX = 0f;
    
    [Tooltip("Y轴偏移 - 用于地图平移")]
    [SerializeField] private float offsetY = 0f;

    [Header("高级选项")]
    [Tooltip("使用脊状噪声")]
    [SerializeField] private bool useRidgedNoise = false;
    
    [Tooltip("脊状噪声混合比例")]
    [Range(0.0f, 1.0f)]
    [SerializeField] private float ridgedNoiseBlend = 0.3f;
    
    [Tooltip("启用实时更新")]
    [SerializeField] private bool enableRealTimeUpdate = true;

    // 属性访问器
    public int Seed => seed;
    public int MapWidth => mapWidth;
    public int MapHeight => mapHeight;
    public int Octaves => octaves;
    public float Frequency => frequency;
    public float Amplitude => amplitude;
    public float Lacunarity => lacunarity;
    public float Persistence => persistence;
    public float RedistributionExponent => redistributionExponent;
    public bool IslandMode => islandMode;
    public float FalloffStrength => falloffStrength;
    public bool EnableEdgeSmoothing => enableEdgeSmoothing;
    public float EdgeSmoothingRange => edgeSmoothingRange;
    public float EdgeSmoothingStrength => edgeSmoothingStrength;
    public float OffsetX => offsetX;
    public float OffsetY => offsetY;
    public bool UseRidgedNoise => useRidgedNoise;
    public float RidgedNoiseBlend => ridgedNoiseBlend;
    public bool EnableRealTimeUpdate => enableRealTimeUpdate;

    // 事件 - 用于通知参数变化
    public System.Action OnConfigChanged;

    /// <summary>
    /// 验证参数有效性
    /// </summary>
    private void OnValidate()
    {
        // 确保地图尺寸为正数
        mapWidth = Mathf.Max(1, mapWidth);
        mapHeight = Mathf.Max(1, mapHeight);
        
        // 确保频率不为零
        frequency = Mathf.Max(0.001f, frequency);
        
        // 确保振幅为正数
        amplitude = Mathf.Max(0.1f, amplitude);
        
        // 触发配置变化事件
        if (enableRealTimeUpdate && OnConfigChanged != null)
        {
            OnConfigChanged.Invoke();
        }
    }

    /// <summary>
    /// 设置种子
    /// </summary>
    /// <param name="newSeed">新种子值</param>
    public void SetSeed(int newSeed)
    {
        seed = newSeed;
        OnConfigChanged?.Invoke();
    }

    /// <summary>
    /// 设置偏移
    /// </summary>
    /// <param name="x">X轴偏移</param>
    /// <param name="y">Y轴偏移</param>
    public void SetOffset(float x, float y)
    {
        offsetX = x;
        offsetY = y;
        OnConfigChanged?.Invoke();
    }

    /// <summary>
    /// 随机化种子
    /// </summary>
    public void RandomizeSeed()
    {
        seed = Random.Range(0, 999999);
        OnConfigChanged?.Invoke();
    }

    /// <summary>
    /// 重置为默认值
    /// </summary>
    public void ResetToDefaults()
    {
        seed = 12345;
        mapWidth = 512;
        mapHeight = 512;
        octaves = 4;
        frequency = 0.01f;
        amplitude = 1.0f;
        lacunarity = 2.0f;
        persistence = 0.5f;
        redistributionExponent = 1.2f;
        islandMode = false;
        falloffStrength = 2.0f;
        enableEdgeSmoothing = true;
        edgeSmoothingRange = 0.1f;
        edgeSmoothingStrength = 1.0f;
        offsetX = 0f;
        offsetY = 0f;
        useRidgedNoise = false;
        ridgedNoiseBlend = 0.3f;
        enableRealTimeUpdate = true;
        
        OnConfigChanged?.Invoke();
    }

    /// <summary>
    /// 获取配置摘要信息
    /// </summary>
    /// <returns>配置摘要字符串</returns>
    public string GetConfigSummary()
    {
        return $"种子: {seed}, 尺寸: {mapWidth}x{mapHeight}, " +
               $"倍频程: {octaves}, 频率: {frequency:F4}, " +
               $"岛屿模式: {(islandMode ? "开启" : "关闭")}";
    }
}
